export interface ChartData {
  name: string;
  value: number;
}

export interface PieChartData {
  name: string;
  value: number;
  color: string;
}

export interface DashboardStat {
  name: string;
  value: string;
  change: number;
  iconName: "users" | "user-group" | "banknotes" | "arrow-trending-up";
  chartData?: ChartData[];
}

export interface ActivityItem {
  id: string;
  type: "payment" | "graduation" | "attendance";
  person: { 
    name: string; 
    href?: string; 
  };
  date: string;
  timestamp: string;
  description: string;
}

/**
 * Interface para dados de gráfico de estudantes
 * 
 * Representa os dados utilizados nos gráficos de linha que mostram
 * a evolução diária dos status dos estudantes ao longo do tempo.
 * 
 * @interface StudentsChartData
 */
export interface StudentsChartData {
  /** Data no formato DD/MM para exibição no gráfico */
  date: string;
  
  /** Número de estudantes ativos nesta data */
  activeStudents: number;
  
  /** Número de estudantes inativos nesta data */
  inactiveStudents: number;
  
  /** 
   * Número de estudantes suspensos nesta data
   * 
   * Campo opcional para manter compatibilidade com implementações
   * que ainda não utilizam o sistema de histórico de status.
   * Quando presente, permite exibir uma terceira linha no gráfico.
   */
  suspendedStudents?: number;
}

export interface DashboardData {
  stats: DashboardStat[];
  recentActivity: ActivityItem[];
  studentsChart: StudentsChartData[];
  instructorsBeltChart: PieChartData[];
  instructorsContractChart: PieChartData[];
}

/**
 * Interface para métricas de estudantes
 * 
 * Contém as métricas principais exibidas no dashboard, comparando
 * dados atuais com dados do período anterior para análise de tendências.
 * 
 * @interface StudentsMetrics
 */
export interface StudentsMetrics {
  /** Número de estudantes ativos no momento atual */
  activeStudentsNow: number | null;
  
  /** Número de estudantes ativos no final do mês anterior */
  activeStudentsLast: number | null;
  
  /** Número de novos estudantes ativos no mês atual */
  newStudentsNow: number | null;
  
  /** Número de novos estudantes ativos no mês anterior */
  newStudentsLast: number | null;
}

export interface RevenueMetrics {
  revenueNow: number;
  revenueLast: number;
}

export interface RetentionRateMetrics {
  retentionRate: number;
  retentionRateLast: number;
}