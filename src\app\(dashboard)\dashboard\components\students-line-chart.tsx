"use client";

import { TrendingDown, TrendingUp } from "lucide-react";
import { CartesianGrid, Line, LineChart, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";
import { StudentsChartData } from "../types";

interface StudentsLineChartProps {
  data: StudentsChartData[];
}

export function StudentsLineChart({ data }: StudentsLineChartProps) {
  const totalActive = data.length > 0 ? data[data.length - 1].activeStudents : 0;
  const startActive = data.length > 0 ? data[0].activeStudents : 0;
  const activeChange = totalActive - startActive;
  const activeChangePercent =
    startActive > 0 ? ((activeChange / startActive) * 100).toFixed(1) : "0";

  // Verificar se há dados de usuários suspensos
  const hasSuspendedData = data.some(item => item.suspendedStudents !== undefined);
  const totalSuspended = data.length > 0 ? (data[data.length - 1].suspendedStudents || 0) : 0;

  const chartConfig = {
    activeStudents: {
      label: "Alunos Ativos",
      color: "hsl(var(--chart-1))",
    },
    inactiveStudents: {
      label: "Alunos Inativos",
      color: "hsl(var(--chart-2))",
    },
    suspendedStudents: {
      label: "Alunos Suspensos",
      color: "hsl(var(--chart-3))",
    },
  } satisfies ChartConfig;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Evolução de Alunos</CardTitle>
        <CardDescription>
          {hasSuspendedData 
            ? "Alunos ativos, inativos e suspensos nos últimos 30 dias"
            : "Alunos ativos e inativos nos últimos 30 dias"
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="p-2">
        <ChartContainer config={chartConfig} className="h-[500px] w-full">
          <LineChart
            accessibilityLayer
            data={data}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
            <Line
              dataKey="activeStudents"
              type="monotone"
              stroke="var(--color-activeStudents)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              dataKey="inactiveStudents"
              type="monotone"
              stroke="var(--color-inactiveStudents)"
              strokeWidth={2}
              dot={false}
            />
            {hasSuspendedData && (
              <Line
                dataKey="suspendedStudents"
                type="monotone"
                stroke="var(--color-suspendedStudents)"
                strokeWidth={2}
                dot={false}
              />
            )}
          </LineChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 font-medium leading-none">
              Variação de {activeChangePercent}% no período
              {activeChange >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
            </div>
            <div className="flex items-center gap-2 leading-none text-muted-foreground">
              Total de {totalActive} alunos ativos
              {hasSuspendedData && totalSuspended > 0 && (
                <span> e {totalSuspended} suspensos</span>
              )} ao final do período.
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
} 