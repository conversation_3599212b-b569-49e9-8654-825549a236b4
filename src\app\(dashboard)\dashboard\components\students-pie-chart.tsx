'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart";
import { StudentsChartData } from '../types';

interface StudentsPieChartProps {
  data: StudentsChartData[];
}

export function StudentsPieChart({ data }: StudentsPieChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="dashboard-stats-card h-[320px] flex items-center justify-center">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Sem dados disponíveis
        </p>
      </div>
    );
  }

  // Pegar os dados mais recentes (último dia)
  const latestData = data[data.length - 1];
  
  // Verificar se há dados de usuários suspensos
  const hasSuspendedData = latestData?.suspendedStudents !== undefined;
  
  const pieData = [
    {
      name: 'Ativos',
      key: 'active',
      value: latestData?.activeStudents || 0,
      fill: 'var(--color-active)',
    },
    {
      name: 'Inativos',
      key: 'inactive',
      value: latestData?.inactiveStudents || 0,
      fill: 'var(--color-inactive)',
    },
  ];

  // Adicionar dados de suspensos se disponível
  if (hasSuspendedData && (latestData?.suspendedStudents || 0) > 0) {
    pieData.push({
      name: 'Suspensos',
      key: 'suspended',
      value: latestData?.suspendedStudents || 0,
      fill: 'var(--color-suspended)',
    });
  }

  const chartConfig: ChartConfig = {
    active: {
      label: 'Ativos',
      color: '#10b981',
    },
    inactive: {
      label: 'Inativos',
      color: '#ef4444',
    },
    suspended: {
      label: 'Suspensos',
      color: '#f59e0b',
    },
  };

  const total = pieData.reduce((sum, entry) => sum + entry.value, 0);

  return (
    <div className="dashboard-stats-card h-[320px]">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base font-semibold text-gray-900 dark:text-white">
          Distribuição de Estudantes
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          Total: {total}
        </span>
      </div>
      
      <div className="h-[180px] flex justify-center">
        <ChartContainer config={chartConfig} className="w-[300px] h-[180px]">
          <PieChart>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              innerRadius={40}
              outerRadius={70}
              paddingAngle={2}
              dataKey="value"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <ChartTooltip content={<ChartTooltipContent nameKey="key" />} />
          </PieChart>
        </ChartContainer>
      </div>
      
      <div className="flex justify-center gap-6 mt-4">
        {pieData.map((entry, index) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: chartConfig[(entry.key as keyof typeof chartConfig)].color }}
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {entry.name}: {entry.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
} 